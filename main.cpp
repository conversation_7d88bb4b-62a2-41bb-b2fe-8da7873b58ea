#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include "mainwindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set application properties
    app.setApplicationName("Counter Application");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("Counter App");
    
    // Set a modern style if available
    QStringList availableStyles = QStyleFactory::keys();
    if (availableStyles.contains("Fusion")) {
        app.setStyle("Fusion");
    }
    
    // Create and show main window
    MainWindow window;
    window.show();
    
    return app.exec();
}
