# Counter Application - Windows API Version

A standalone C++ counter application with 9 circular counters in a 3x3 grid, built using native Windows API.

## ✅ **Features**

- **9 Circular Counters**: Arranged in a responsive 3x3 grid layout
- **Individual Controls**: Each counter has +/- buttons and a reset button
- **Keyboard Input**: Press number keys 1-9 to increment corresponding counters
- **Global Reset**: Reset all counters at once with confirmation dialog
- **Confirmation Dialogs**: All reset operations require user confirmation
- **Responsive Design**: Layout automatically adjusts to window size
- **Standalone Executable**: No external dependencies required

## 🚀 **Ready to Use**

The executable `CounterApp.exe` is already built and ready to run!

Simply double-click `CounterApp.exe` to start the application.

## 📋 **How to Use**

1. **Increment Counters**: 
   - Click the "+" button below each counter
   - Press number keys 1-9 to increment the corresponding counter

2. **Decrement Counters**: 
   - Click the "-" button below each counter
   - Counters cannot go below 0

3. **Reset Individual Counter**: 
   - Click the "Reset" button below each counter
   - Confirm the action in the dialog that appears

4. **Reset All Counters**: 
   - Click the "Reset All Counters" button at the bottom
   - Confirm the action in the dialog that appears

5. **Responsive Layout**: 
   - Resize the window to see the counters automatically adjust

## 🔧 **Technical Details**

- **Language**: C++ with Windows API
- **Compiler**: Microsoft Visual C++ (MSVC)
- **Dependencies**: None (uses only Windows system libraries)
- **Architecture**: 64-bit Windows executable

## 🏗️ **Rebuilding (Optional)**

If you want to rebuild the application:

1. Run `build_win32.bat` 
2. The script will automatically detect and use Visual Studio or MinGW
3. The new executable will be created as `CounterApp.exe`

## 📁 **Files**

- `CounterApp.exe` - The main executable (ready to run)
- `counter_app_win32.cpp` - Source code
- `build_win32.bat` - Build script
- `README_WIN32.md` - This documentation

## 🎯 **Counter Layout**

The counters are numbered 1-9 and arranged as follows:

```
1  2  3
4  5  6
7  8  9
```

Each counter displays:
- Counter number (top of circle)
- Current value (center of circle)
- +/- buttons (below circle)
- Reset button (below +/- buttons)

## 🖥️ **System Requirements**

- Windows 7 or later
- No additional software required
- Approximately 1 MB disk space
