@echo off
echo Building Counter Application...

REM Create build directory
if not exist build mkdir build
cd build

REM Run CMake to generate build files
cmake .. -G "MinGW Makefiles"
if %errorlevel% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build the application
cmake --build .
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable is located at: build\CounterApp.exe
pause
