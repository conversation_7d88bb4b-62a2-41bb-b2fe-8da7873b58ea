#include "mainwindow.h"
#include <QApplication>
#include <QKeyEvent>
#include <QMessageBox>
#include <QLabel>
#include <QFont>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_gridLayout(nullptr)
    , m_resetAllButton(nullptr)
    , m_titleLabel(nullptr)
    , m_instructionLabel(nullptr)
{
    setupUI();
    setMinimumSize(MIN_WINDOW_WIDTH, MIN_WINDOW_HEIGHT);
    resize(800, 700);
    setWindowTitle("Counter Application");
    
    // Set focus policy to receive key events
    setFocusPolicy(Qt::StrongFocus);
}

MainWindow::~MainWindow()
{
    // Counters will be automatically deleted by Qt's parent-child system
}

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setContentsMargins(15, 15, 15, 15);
    
    // Create title label
    m_titleLabel = new QLabel("Counter Application", this);
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(18);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_titleLabel->setStyleSheet("QLabel { color: #333; margin-bottom: 5px; }");
    
    // Create instruction label
    m_instructionLabel = new QLabel("Use numpad keys 1-9 to increment counters", this);
    QFont instructionFont = m_instructionLabel->font();
    instructionFont.setPointSize(10);
    m_instructionLabel->setFont(instructionFont);
    m_instructionLabel->setAlignment(Qt::AlignCenter);
    m_instructionLabel->setStyleSheet("QLabel { color: #666; margin-bottom: 10px; }");
    
    // Add title and instruction to layout
    m_mainLayout->addWidget(m_titleLabel);
    m_mainLayout->addWidget(m_instructionLabel);
    
    // Create grid layout for counters
    m_gridLayout = new QGridLayout();
    m_gridLayout->setSpacing(10);
    
    // Create 9 counters and add them to the grid
    for (int i = 0; i < 9; ++i) {
        int row = i / GRID_COLS;
        int col = i % GRID_COLS;
        
        m_counters[i] = new Counter(i + 1, this);
        m_gridLayout->addWidget(m_counters[i], row, col);
    }
    
    // Make grid layout stretch equally
    for (int i = 0; i < GRID_ROWS; ++i) {
        m_gridLayout->setRowStretch(i, 1);
    }
    for (int i = 0; i < GRID_COLS; ++i) {
        m_gridLayout->setColumnStretch(i, 1);
    }
    
    m_mainLayout->addLayout(m_gridLayout, 1); // Give grid layout most of the space
    
    // Create reset all button
    m_resetAllButton = new QPushButton("Reset All Counters", this);
    m_resetAllButton->setFixedHeight(40);
    m_resetAllButton->setStyleSheet(
        "QPushButton {"
        "    font-weight: bold;"
        "    font-size: 12px;"
        "    border-radius: 8px;"
        "    border: 2px solid #d32f2f;"
        "    background-color: #ffebee;"
        "    color: #d32f2f;"
        "    padding: 8px 16px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #ffcdd2;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #ef9a9a;"
        "}"
    );
    
    connect(m_resetAllButton, &QPushButton::clicked, this, &MainWindow::onResetAllClicked);
    
    m_mainLayout->addWidget(m_resetAllButton);
}

void MainWindow::keyPressEvent(QKeyEvent *event)
{
    // Handle numpad keys 1-9
    if (event->key() >= Qt::Key_1 && event->key() <= Qt::Key_9) {
        int counterIndex = event->key() - Qt::Key_1; // Convert to 0-8 index
        if (counterIndex < 9) {
            m_counters[counterIndex]->increment();
        }
        return;
    }
    
    // Handle regular number keys 1-9 as well
    if (event->text().length() == 1) {
        QChar ch = event->text().at(0);
        if (ch >= '1' && ch <= '9') {
            int counterIndex = ch.digitValue() - 1; // Convert to 0-8 index
            if (counterIndex < 9) {
                m_counters[counterIndex]->increment();
            }
            return;
        }
    }
    
    QMainWindow::keyPressEvent(event);
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);
    updateLayout();
}

void MainWindow::updateLayout()
{
    // The grid layout automatically handles responsiveness
    // This method can be used for additional layout adjustments if needed
}

void MainWindow::onResetAllClicked()
{
    QMessageBox::StandardButton reply = QMessageBox::question(
        this,
        "Reset All Counters",
        "Are you sure you want to reset all counters to zero?",
        QMessageBox::Yes | QMessageBox::No,
        QMessageBox::No
    );
    
    if (reply == QMessageBox::Yes) {
        for (auto* counter : m_counters) {
            counter->reset();
        }
    }
}
