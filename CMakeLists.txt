cmake_minimum_required(VERSION 3.16)
project(CounterApp VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6 components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets)

# Enable Qt's MOC (Meta-Object Compiler)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Source files
set(SOURCES
    main.cpp
    mainwindow.cpp
    counter.cpp
)

# Header files
set(HEADERS
    mainwindow.h
    counter.h
)

# Create executable
add_executable(CounterApp ${SOURCES} ${HEADERS})

# Link Qt libraries
target_link_libraries(CounterApp Qt6::Core Qt6::Widgets)

# Set target properties
set_target_properties(CounterApp PROPERTIES
    WIN32_EXECUTABLE TRUE
    MACOSX_BUNDLE TRUE
)
