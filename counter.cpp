#include "counter.h"
#include <QApplication>
#include <QPainter>
#include <QPen>
#include <QBrush>
#include <QResizeEvent>
#include <QMessageBox>
#include <algorithm>

Counter::Counter(int counterNumber, QWidget *parent)
    : QWidget(parent)
    , m_value(0)
    , m_counterNumber(counterNumber)
    , m_incrementButton(nullptr)
    , m_decrementButton(nullptr)
    , m_resetButton(nullptr)
    , m_mainLayout(nullptr)
    , m_buttonLayout(nullptr)
{
    setupUI();
    setMinimumSize(MIN_CIRCLE_SIZE + 20, MIN_CIRCLE_SIZE + BUTTON_HEIGHT * 2 + SPACING * 3);
}

void Counter::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(SPACING);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    
    // Add stretch to center the circle
    m_mainLayout->addStretch();
    
    // Create button layout
    m_buttonLayout = new QHBoxLayout();
    m_buttonLayout->setSpacing(SPACING);
    
    // Create buttons
    m_decrementButton = new QPushButton("-", this);
    m_incrementButton = new QPushButton("+", this);
    m_resetButton = new QPushButton("Reset", this);
    
    // Set button properties
    m_decrementButton->setFixedHeight(BUTTON_HEIGHT);
    m_incrementButton->setFixedHeight(BUTTON_HEIGHT);
    m_resetButton->setFixedHeight(BUTTON_HEIGHT);
    
    // Style buttons
    QString buttonStyle = "QPushButton { font-weight: bold; border-radius: 5px; border: 2px solid #555; background-color: #f0f0f0; }"
                         "QPushButton:hover { background-color: #e0e0e0; }"
                         "QPushButton:pressed { background-color: #d0d0d0; }";
    
    m_decrementButton->setStyleSheet(buttonStyle);
    m_incrementButton->setStyleSheet(buttonStyle);
    m_resetButton->setStyleSheet(buttonStyle + "QPushButton { background-color: #ffeeee; }"
                                              "QPushButton:hover { background-color: #ffdddd; }");
    
    // Add buttons to layout
    m_buttonLayout->addWidget(m_decrementButton);
    m_buttonLayout->addWidget(m_incrementButton);
    
    m_mainLayout->addLayout(m_buttonLayout);
    m_mainLayout->addWidget(m_resetButton);
    m_mainLayout->addStretch();
    
    // Connect signals
    connect(m_incrementButton, &QPushButton::clicked, this, &Counter::onIncrementClicked);
    connect(m_decrementButton, &QPushButton::clicked, this, &Counter::onDecrementClicked);
    connect(m_resetButton, &QPushButton::clicked, this, &Counter::onResetClicked);
}

void Counter::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    QRect circleRect = getCircleRect();
    
    // Draw circle background
    painter.setBrush(QBrush(QColor(240, 240, 255)));
    painter.setPen(QPen(QColor(100, 100, 150), 3));
    painter.drawEllipse(circleRect);
    
    // Draw counter number in top part of circle
    QFont numberFont = font();
    numberFont.setPointSize(std::max(8, circleRect.width() / 8));
    numberFont.setBold(true);
    painter.setFont(numberFont);
    painter.setPen(QColor(80, 80, 120));
    
    QRect numberRect = circleRect;
    numberRect.setHeight(circleRect.height() / 3);
    painter.drawText(numberRect, Qt::AlignCenter, QString::number(m_counterNumber));
    
    // Draw value in center of circle
    QFont valueFont = font();
    valueFont.setPointSize(std::max(12, circleRect.width() / 6));
    valueFont.setBold(true);
    painter.setFont(valueFont);
    painter.setPen(QColor(50, 50, 100));
    
    QRect valueRect = circleRect;
    valueRect.setTop(circleRect.top() + circleRect.height() / 3);
    valueRect.setHeight(circleRect.height() / 2);
    painter.drawText(valueRect, Qt::AlignCenter, QString::number(m_value));
}

QRect Counter::getCircleRect() const
{
    int availableHeight = height() - BUTTON_HEIGHT * 2 - SPACING * 4 - 10; // 10 for margins
    int availableWidth = width() - 10; // 10 for margins
    
    int circleSize = std::min(availableWidth, availableHeight);
    circleSize = std::max(circleSize, MIN_CIRCLE_SIZE);
    
    int x = (width() - circleSize) / 2;
    int y = 5; // Top margin
    
    return QRect(x, y, circleSize, circleSize);
}

void Counter::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    updateButtonSizes();
}

void Counter::updateButtonSizes()
{
    int buttonWidth = (width() - SPACING * 3 - 10) / 2; // 10 for margins, 3 spacings
    buttonWidth = std::max(buttonWidth, 40);
    
    m_decrementButton->setFixedWidth(buttonWidth);
    m_incrementButton->setFixedWidth(buttonWidth);
    m_resetButton->setMinimumWidth(buttonWidth * 2 + SPACING);
}

void Counter::setValue(int value)
{
    m_value = value;
    update(); // Trigger repaint
}

void Counter::increment()
{
    m_value++;
    update();
}

void Counter::decrement()
{
    if (m_value > 0) {
        m_value--;
        update();
    }
}

void Counter::reset()
{
    m_value = 0;
    update();
}

void Counter::onIncrementClicked()
{
    increment();
}

void Counter::onDecrementClicked()
{
    decrement();
}

void Counter::onResetClicked()
{
    QMessageBox::StandardButton reply = QMessageBox::question(
        this,
        "Reset Counter",
        QString("Are you sure you want to reset counter %1?").arg(m_counterNumber),
        QMessageBox::Yes | QMessageBox::No,
        QMessageBox::No
    );
    
    if (reply == QMessageBox::Yes) {
        reset();
    }
}
