#define UNICODE
#define _UNICODE
#include <windows.h>
#include <windowsx.h>
#include <commctrl.h>
#include <string>
#include <vector>
#include <cmath>

#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "user32.lib")

// Constants
const int WINDOW_WIDTH = 900;
const int WINDOW_HEIGHT = 750;
const int GRID_SIZE = 3;
const int COUNTER_SIZE = 200;
const int BUTTON_HEIGHT = 35;
const int BUTTON_WIDTH = 70;
const int MARGIN = 25;

// Control IDs
const int ID_COUNTER_BASE = 1000;
const int ID_INCREMENT_BASE = 2000;
const int ID_DECREMENT_BASE = 3000;
const int ID_RESET_BASE = 4000;
const int ID_RESET_ALL = 5000;

// Global variables
HINSTANCE g_hInst;
HWND g_hMainWnd;
std::vector<int> g_counterValues(9, 0);
std::vector<HWND> g_counterLabels(9);
std::vector<HWND> g_incrementButtons(9);
std::vector<HWND> g_decrementButtons(9);
std::vector<HWND> g_resetButtons(9);
HWND g_resetAllButton;
HFONT g_titleFont, g_counterFont, g_buttonFont, g_numberFont;
HBRUSH g_backgroundBrush;

// Function declarations
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void CreateControls(HWND hwnd);
void UpdateCounterDisplay(int index);
void ResizeControls(HWND hwnd);
void DrawCircularCounter(HDC hdc, RECT rect, int counterNum, int value);
LRESULT CALLBACK CounterProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
LRESULT CALLBACK CustomButtonProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void IncrementCounter(int index);
void DecrementCounter(int index);
void ResetCounter(int index);
void ResetAllCounters();
void DrawCustomButton(HDC hdc, RECT rect, LPCTSTR text, BOOL isPressed, BOOL isHover, COLORREF bgColor);
WNDPROC g_originalStaticProc;
WNDPROC g_originalButtonProc;

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    g_hInst = hInstance;
    
    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_WIN95_CLASSES;
    InitCommonControlsEx(&icex);
    
    // Register window class
    WNDCLASSEX wc = {};
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hbrBackground = CreateSolidBrush(RGB(240, 245, 255)); // Light blue-gray background
    wc.lpszClassName = TEXT("CounterApp");
    wc.hIconSm = LoadIcon(NULL, IDI_APPLICATION);
    
    if (!RegisterClassEx(&wc))
    {
        MessageBox(NULL, TEXT("Window Registration Failed!"), TEXT("Error"), MB_ICONEXCLAMATION | MB_OK);
        return 0;
    }
    
    // Create fonts
    g_titleFont = CreateFont(32, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, DEFAULT_CHARSET,
                            OUT_OUTLINE_PRECIS, CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                            VARIABLE_PITCH, TEXT("Segoe UI"));
    g_counterFont = CreateFont(28, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, DEFAULT_CHARSET,
                              OUT_OUTLINE_PRECIS, CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                              VARIABLE_PITCH, TEXT("Segoe UI"));
    g_numberFont = CreateFont(16, 0, 0, 0, FW_SEMIBOLD, FALSE, FALSE, FALSE, DEFAULT_CHARSET,
                             OUT_OUTLINE_PRECIS, CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                             VARIABLE_PITCH, TEXT("Segoe UI"));
    g_buttonFont = CreateFont(14, 0, 0, 0, FW_SEMIBOLD, FALSE, FALSE, FALSE, DEFAULT_CHARSET,
                             OUT_OUTLINE_PRECIS, CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                             VARIABLE_PITCH, TEXT("Segoe UI"));

    // Create background brush
    g_backgroundBrush = CreateSolidBrush(RGB(240, 245, 255));
    
    // Create main window
    g_hMainWnd = CreateWindowEx(
        WS_EX_CLIENTEDGE,
        TEXT("CounterApp"),
        TEXT("Counter Application"),
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        WINDOW_WIDTH, WINDOW_HEIGHT,
        NULL, NULL, hInstance, NULL);
    
    if (g_hMainWnd == NULL)
    {
        MessageBox(NULL, TEXT("Window Creation Failed!"), TEXT("Error"), MB_ICONEXCLAMATION | MB_OK);
        return 0;
    }
    
    ShowWindow(g_hMainWnd, nCmdShow);
    UpdateWindow(g_hMainWnd);
    
    // Message loop
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0) > 0)
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    // Cleanup fonts and brushes
    DeleteObject(g_titleFont);
    DeleteObject(g_counterFont);
    DeleteObject(g_numberFont);
    DeleteObject(g_buttonFont);
    DeleteObject(g_backgroundBrush);
    
    return msg.wParam;
}

LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
    case WM_CREATE:
        CreateControls(hwnd);
        SetFocus(hwnd); // Ensure the main window has focus for keyboard input
        break;
        
    case WM_SIZE:
        ResizeControls(hwnd);
        break;
        
    case WM_COMMAND:
        {
            int wmId = LOWORD(wParam);
            
            if (wmId >= ID_INCREMENT_BASE && wmId < ID_INCREMENT_BASE + 9)
            {
                IncrementCounter(wmId - ID_INCREMENT_BASE);
            }
            else if (wmId >= ID_DECREMENT_BASE && wmId < ID_DECREMENT_BASE + 9)
            {
                DecrementCounter(wmId - ID_DECREMENT_BASE);
            }
            else if (wmId >= ID_RESET_BASE && wmId < ID_RESET_BASE + 9)
            {
                std::wstring message = L"Are you sure you want to reset counter " + std::to_wstring(wmId - ID_RESET_BASE + 1) + L"?";
                int result = MessageBox(hwnd, message.c_str(), TEXT("Reset Counter"), MB_YESNO | MB_ICONQUESTION);
                if (result == IDYES)
                {
                    ResetCounter(wmId - ID_RESET_BASE);
                }
            }
            else if (wmId == ID_RESET_ALL)
            {
                int result = MessageBox(hwnd, TEXT("Are you sure you want to reset all counters?"),
                    TEXT("Reset All Counters"), MB_YESNO | MB_ICONQUESTION);
                if (result == IDYES)
                {
                    ResetAllCounters();
                }
            }
        }
        break;

    case WM_DRAWITEM:
        {
            DRAWITEMSTRUCT* dis = (DRAWITEMSTRUCT*)lParam;
            if (dis->CtlID >= ID_COUNTER_BASE && dis->CtlID < ID_COUNTER_BASE + 9)
            {
                int counterIndex = dis->CtlID - ID_COUNTER_BASE;
                DrawCircularCounter(dis->hDC, dis->rcItem, counterIndex + 1, g_counterValues[counterIndex]);
                return TRUE;
            }
            else if (dis->CtlID >= ID_INCREMENT_BASE && dis->CtlID < ID_INCREMENT_BASE + 9)
            {
                BOOL isPressed = (dis->itemState & ODS_SELECTED);
                DrawCustomButton(dis->hDC, dis->rcItem, TEXT("+"), isPressed, FALSE, RGB(220, 255, 220));
                return TRUE;
            }
            else if (dis->CtlID >= ID_DECREMENT_BASE && dis->CtlID < ID_DECREMENT_BASE + 9)
            {
                BOOL isPressed = (dis->itemState & ODS_SELECTED);
                DrawCustomButton(dis->hDC, dis->rcItem, TEXT("-"), isPressed, FALSE, RGB(255, 220, 220));
                return TRUE;
            }
            else if (dis->CtlID >= ID_RESET_BASE && dis->CtlID < ID_RESET_BASE + 9)
            {
                BOOL isPressed = (dis->itemState & ODS_SELECTED);
                DrawCustomButton(dis->hDC, dis->rcItem, TEXT("Reset"), isPressed, FALSE, RGB(255, 240, 200));
                return TRUE;
            }
            else if (dis->CtlID == ID_RESET_ALL)
            {
                BOOL isPressed = (dis->itemState & ODS_SELECTED);
                DrawCustomButton(dis->hDC, dis->rcItem, TEXT("Reset All Counters"), isPressed, FALSE, RGB(255, 200, 200));
                return TRUE;
            }
        }
        break;

    case WM_KEYDOWN:
        {
            // Handle numpad keys (VK_NUMPAD1 to VK_NUMPAD9)
            if (wParam >= VK_NUMPAD1 && wParam <= VK_NUMPAD9)
            {
                IncrementCounter(wParam - VK_NUMPAD1);
                return 0; // Indicate we handled the message
            }
            // Handle regular number keys (VK_1 to VK_9) - these are the top row keys
            else if (wParam >= 0x31 && wParam <= 0x39) // 0x31 = '1', 0x39 = '9'
            {
                IncrementCounter(wParam - 0x31);
                return 0; // Indicate we handled the message
            }
        }
        break;

    case WM_LBUTTONDOWN:
    case WM_RBUTTONDOWN:
        // Ensure window has focus when clicked
        SetFocus(hwnd);
        break;

    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);

            // Get client rect
            RECT clientRect;
            GetClientRect(hwnd, &clientRect);

            // Fill background with gradient-like effect
            HBRUSH gradientBrush = CreateSolidBrush(RGB(245, 250, 255));
            FillRect(hdc, &clientRect, gradientBrush);
            DeleteObject(gradientBrush);

            // Draw title with shadow effect
            SetBkMode(hdc, TRANSPARENT);
            SelectObject(hdc, g_titleFont);

            // Shadow
            SetTextColor(hdc, RGB(200, 200, 220));
            RECT shadowRect = {1, 16, clientRect.right + 1, 55};
            DrawText(hdc, TEXT("Counter Application"), -1, &shadowRect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);

            // Main title
            SetTextColor(hdc, RGB(45, 55, 85));
            RECT titleRect = {0, 15, clientRect.right, 54};
            DrawText(hdc, TEXT("Counter Application"), -1, &titleRect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);

            // Instruction text
            SelectObject(hdc, g_buttonFont);
            SetTextColor(hdc, RGB(85, 95, 115));
            RECT instrRect = {0, 50, clientRect.right, 75};
            DrawText(hdc, TEXT("Press number keys 1-9 or use mouse controls"), -1, &instrRect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);

            EndPaint(hwnd, &ps);
        }
        break;
        
    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
    return 0;
}

void CreateControls(HWND hwnd)
{
    // Create counter displays (static controls for custom drawing)
    for (int i = 0; i < 9; i++)
    {
        g_counterLabels[i] = CreateWindow(TEXT("STATIC"), TEXT(""),
            WS_CHILD | WS_VISIBLE | SS_OWNERDRAW,
            0, 0, COUNTER_SIZE, COUNTER_SIZE,
            hwnd, (HMENU)(ID_COUNTER_BASE + i), g_hInst, NULL);
    }

    // Create increment buttons
    for (int i = 0; i < 9; i++)
    {
        g_incrementButtons[i] = CreateWindow(TEXT("BUTTON"), TEXT("+"),
            WS_CHILD | WS_VISIBLE | BS_OWNERDRAW,
            0, 0, BUTTON_WIDTH, BUTTON_HEIGHT,
            hwnd, (HMENU)(ID_INCREMENT_BASE + i), g_hInst, NULL);
    }

    // Create decrement buttons
    for (int i = 0; i < 9; i++)
    {
        g_decrementButtons[i] = CreateWindow(TEXT("BUTTON"), TEXT("-"),
            WS_CHILD | WS_VISIBLE | BS_OWNERDRAW,
            0, 0, BUTTON_WIDTH, BUTTON_HEIGHT,
            hwnd, (HMENU)(ID_DECREMENT_BASE + i), g_hInst, NULL);
    }

    // Create reset buttons
    for (int i = 0; i < 9; i++)
    {
        g_resetButtons[i] = CreateWindow(TEXT("BUTTON"), TEXT("Reset"),
            WS_CHILD | WS_VISIBLE | BS_OWNERDRAW,
            0, 0, BUTTON_WIDTH * 2 + 10, BUTTON_HEIGHT,
            hwnd, (HMENU)(ID_RESET_BASE + i), g_hInst, NULL);
    }

    // Create reset all button
    g_resetAllButton = CreateWindow(TEXT("BUTTON"), TEXT("Reset All Counters"),
        WS_CHILD | WS_VISIBLE | BS_OWNERDRAW,
        0, 0, 220, 45,
        hwnd, (HMENU)ID_RESET_ALL, g_hInst, NULL);
    SendMessage(g_resetAllButton, WM_SETFONT, (WPARAM)g_buttonFont, TRUE);
}

void ResizeControls(HWND hwnd)
{
    RECT clientRect;
    GetClientRect(hwnd, &clientRect);

    int clientWidth = clientRect.right - clientRect.left;
    int clientHeight = clientRect.bottom - clientRect.top;

    // Calculate grid dimensions
    int gridWidth = clientWidth - 2 * MARGIN;
    int gridHeight = clientHeight - 150; // Leave space for title and reset all button

    int cellWidth = gridWidth / GRID_SIZE;
    int cellHeight = gridHeight / GRID_SIZE;

    int counterSize = min(cellWidth - 20, cellHeight - 80) - 20; // Leave space for buttons
    counterSize = max(counterSize, 80); // Minimum size

    // Position counters and buttons
    for (int i = 0; i < 9; i++)
    {
        int row = i / GRID_SIZE;
        int col = i % GRID_SIZE;

        int x = MARGIN + col * cellWidth + (cellWidth - counterSize) / 2;
        int y = 80 + row * cellHeight + (cellHeight - counterSize - 80) / 2; // 80 for title space

        // Position counter display
        SetWindowPos(g_counterLabels[i], NULL, x, y, counterSize, counterSize,
                    SWP_NOZORDER);

        // Position increment and decrement buttons
        int buttonY = y + counterSize + 10;
        int button1X = x + (counterSize - BUTTON_WIDTH * 2 - 10) / 2;
        int button2X = button1X + BUTTON_WIDTH + 10;

        SetWindowPos(g_decrementButtons[i], NULL, button1X, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT,
                    SWP_NOZORDER);
        SetWindowPos(g_incrementButtons[i], NULL, button2X, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT,
                    SWP_NOZORDER);

        // Position reset button
        int resetButtonY = buttonY + BUTTON_HEIGHT + 5;
        int resetButtonX = x + (counterSize - (BUTTON_WIDTH * 2 + 10)) / 2;
        SetWindowPos(g_resetButtons[i], NULL, resetButtonX, resetButtonY, BUTTON_WIDTH * 2 + 10, BUTTON_HEIGHT,
                    SWP_NOZORDER);
    }

    // Position reset all button
    int resetAllX = (clientWidth - 220) / 2;
    int resetAllY = clientHeight - 70;
    SetWindowPos(g_resetAllButton, NULL, resetAllX, resetAllY, 220, 45, SWP_NOZORDER);
}

void UpdateCounterDisplay(int index)
{
    InvalidateRect(g_counterLabels[index], NULL, TRUE);
}

void IncrementCounter(int index)
{
    if (index >= 0 && index < 9)
    {
        g_counterValues[index]++;
        UpdateCounterDisplay(index);
    }
}

void DecrementCounter(int index)
{
    if (index >= 0 && index < 9 && g_counterValues[index] > 0)
    {
        g_counterValues[index]--;
        UpdateCounterDisplay(index);
    }
}

void ResetCounter(int index)
{
    if (index >= 0 && index < 9)
    {
        g_counterValues[index] = 0;
        UpdateCounterDisplay(index);
    }
}

void ResetAllCounters()
{
    for (int i = 0; i < 9; i++)
    {
        g_counterValues[i] = 0;
        UpdateCounterDisplay(i);
    }
}

LRESULT CALLBACK CounterProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);

            RECT rect;
            GetClientRect(hwnd, &rect);

            // Find which counter this is
            int counterIndex = -1;
            for (int i = 0; i < 9; i++)
            {
                if (g_counterLabels[i] == hwnd)
                {
                    counterIndex = i;
                    break;
                }
            }

            if (counterIndex >= 0)
            {
                DrawCircularCounter(hdc, rect, counterIndex + 1, g_counterValues[counterIndex]);
            }

            EndPaint(hwnd, &ps);
        }
        return 0;

    default:
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
}

void DrawCircularCounter(HDC hdc, RECT rect, int counterNum, int value)
{
    int centerX = (rect.left + rect.right) / 2;
    int centerY = (rect.top + rect.bottom) / 2;
    int radius = min(rect.right - rect.left, rect.bottom - rect.top) / 2 - 8;

    // Draw outer shadow circle
    HBRUSH shadowBrush = CreateSolidBrush(RGB(200, 210, 230));
    HPEN shadowPen = CreatePen(PS_SOLID, 1, RGB(200, 210, 230));
    HBRUSH oldBrush = (HBRUSH)SelectObject(hdc, shadowBrush);
    HPEN oldPen = (HPEN)SelectObject(hdc, shadowPen);

    Ellipse(hdc, centerX - radius + 2, centerY - radius + 2,
            centerX + radius + 2, centerY + radius + 2);

    // Draw main circle with gradient-like effect
    HBRUSH mainBrush = CreateSolidBrush(RGB(255, 255, 255));
    HPEN mainPen = CreatePen(PS_SOLID, 2, RGB(70, 130, 200));
    SelectObject(hdc, mainBrush);
    SelectObject(hdc, mainPen);

    Ellipse(hdc, centerX - radius, centerY - radius,
            centerX + radius, centerY + radius);

    // Draw inner highlight circle
    HBRUSH highlightBrush = CreateSolidBrush(RGB(245, 250, 255));
    HPEN highlightPen = CreatePen(PS_SOLID, 1, RGB(180, 200, 230));
    SelectObject(hdc, highlightBrush);
    SelectObject(hdc, highlightPen);

    int innerRadius = radius - 6;
    Ellipse(hdc, centerX - innerRadius, centerY - innerRadius,
            centerX + innerRadius, centerY + innerRadius);

    // Set text properties
    SetBkMode(hdc, TRANSPARENT);
    SetTextAlign(hdc, TA_CENTER | TA_BASELINE);

    // Draw counter number (small, at top)
    SelectObject(hdc, g_numberFont);
    SetTextColor(hdc, RGB(100, 120, 150));
    int numberY = centerY - radius / 3;

    std::wstring counterText = std::to_wstring(counterNum);
    TextOut(hdc, centerX, numberY, counterText.c_str(), counterText.length());

    // Draw value (large, in center)
    SelectObject(hdc, g_counterFont);
    SetTextColor(hdc, RGB(40, 60, 90));
    int valueY = centerY + 8;

    std::wstring valueText = std::to_wstring(value);
    TextOut(hdc, centerX, valueY, valueText.c_str(), valueText.length());

    // Cleanup
    SelectObject(hdc, oldBrush);
    SelectObject(hdc, oldPen);
    DeleteObject(shadowBrush);
    DeleteObject(shadowPen);
    DeleteObject(mainBrush);
    DeleteObject(mainPen);
    DeleteObject(highlightBrush);
    DeleteObject(highlightPen);
}

void DrawCustomButton(HDC hdc, RECT rect, LPCTSTR text, BOOL isPressed, BOOL isHover, COLORREF bgColor)
{
    // Draw button shadow
    if (!isPressed) {
        HBRUSH shadowBrush = CreateSolidBrush(RGB(180, 190, 210));
        RECT shadowRect = {rect.left + 2, rect.top + 2, rect.right + 2, rect.bottom + 2};
        FillRect(hdc, &shadowRect, shadowBrush);
        DeleteObject(shadowBrush);
    }

    // Draw main button
    HBRUSH buttonBrush;
    if (isPressed) {
        buttonBrush = CreateSolidBrush(RGB(GetRValue(bgColor) - 30, GetGValue(bgColor) - 30, GetBValue(bgColor) - 30));
    } else if (isHover) {
        buttonBrush = CreateSolidBrush(RGB(GetRValue(bgColor) + 20, GetGValue(bgColor) + 20, GetBValue(bgColor) + 20));
    } else {
        buttonBrush = CreateSolidBrush(bgColor);
    }

    HPEN buttonPen = CreatePen(PS_SOLID, 1, RGB(120, 140, 170));
    HBRUSH oldBrush = (HBRUSH)SelectObject(hdc, buttonBrush);
    HPEN oldPen = (HPEN)SelectObject(hdc, buttonPen);

    RECT buttonRect;
    if (isPressed) {
        buttonRect.left = rect.left + 1;
        buttonRect.top = rect.top + 1;
        buttonRect.right = rect.right + 1;
        buttonRect.bottom = rect.bottom + 1;
    } else {
        buttonRect = rect;
    }

    RoundRect(hdc, buttonRect.left, buttonRect.top, buttonRect.right, buttonRect.bottom, 8, 8);

    // Draw button text
    SetBkMode(hdc, TRANSPARENT);
    SetTextAlign(hdc, TA_CENTER | TA_BASELINE);
    SelectObject(hdc, g_buttonFont);
    SetTextColor(hdc, RGB(50, 70, 100));

    int textX = (buttonRect.left + buttonRect.right) / 2;
    int textY = (buttonRect.top + buttonRect.bottom) / 2 + 4;

    TextOut(hdc, textX, textY, text, lstrlen(text));

    // Cleanup
    SelectObject(hdc, oldBrush);
    SelectObject(hdc, oldPen);
    DeleteObject(buttonBrush);
    DeleteObject(buttonPen);
}
