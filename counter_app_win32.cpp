#define UNICODE
#define _UNICODE
#include <windows.h>
#include <windowsx.h>
#include <commctrl.h>
#include <string>
#include <vector>
#include <cmath>

#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "user32.lib")

// Constants
const int WINDOW_WIDTH = 800;
const int WINDOW_HEIGHT = 700;
const int GRID_SIZE = 3;
const int COUNTER_SIZE = 180;
const int BUTTON_HEIGHT = 30;
const int BUTTON_WIDTH = 60;
const int MARGIN = 20;

// Control IDs
const int ID_COUNTER_BASE = 1000;
const int ID_INCREMENT_BASE = 2000;
const int ID_DECREMENT_BASE = 3000;
const int ID_RESET_BASE = 4000;
const int ID_RESET_ALL = 5000;

// Global variables
HINSTANCE g_hInst;
HWND g_hMainWnd;
std::vector<int> g_counterValues(9, 0);
std::vector<HWND> g_counterLabels(9);
std::vector<HWND> g_incrementButtons(9);
std::vector<HWND> g_decrementButtons(9);
std::vector<HWND> g_resetButtons(9);
HWND g_resetAllButton;
HFONT g_titleFont, g_counterFont, g_buttonFont;

// Function declarations
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void CreateControls(HWND hwnd);
void UpdateCounterDisplay(int index);
void ResizeControls(HWND hwnd);
void DrawCircularCounter(HDC hdc, RECT rect, int counterNum, int value);
LRESULT CALLBACK CounterProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void IncrementCounter(int index);
void DecrementCounter(int index);
void ResetCounter(int index);
void ResetAllCounters();
WNDPROC g_originalStaticProc;

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    g_hInst = hInstance;
    
    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_WIN95_CLASSES;
    InitCommonControlsEx(&icex);
    
    // Register window class
    WNDCLASSEX wc = {};
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.lpszClassName = TEXT("CounterApp");
    wc.hIconSm = LoadIcon(NULL, IDI_APPLICATION);
    
    if (!RegisterClassEx(&wc))
    {
        MessageBox(NULL, TEXT("Window Registration Failed!"), TEXT("Error"), MB_ICONEXCLAMATION | MB_OK);
        return 0;
    }
    
    // Create fonts
    g_titleFont = CreateFont(24, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, DEFAULT_CHARSET,
                            OUT_OUTLINE_PRECIS, CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                            VARIABLE_PITCH, TEXT("Arial"));
    g_counterFont = CreateFont(20, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE, DEFAULT_CHARSET,
                              OUT_OUTLINE_PRECIS, CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                              VARIABLE_PITCH, TEXT("Arial"));
    g_buttonFont = CreateFont(16, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE, DEFAULT_CHARSET,
                             OUT_OUTLINE_PRECIS, CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                             VARIABLE_PITCH, TEXT("Arial"));
    
    // Create main window
    g_hMainWnd = CreateWindowEx(
        WS_EX_CLIENTEDGE,
        TEXT("CounterApp"),
        TEXT("Counter Application"),
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        WINDOW_WIDTH, WINDOW_HEIGHT,
        NULL, NULL, hInstance, NULL);
    
    if (g_hMainWnd == NULL)
    {
        MessageBox(NULL, TEXT("Window Creation Failed!"), TEXT("Error"), MB_ICONEXCLAMATION | MB_OK);
        return 0;
    }
    
    ShowWindow(g_hMainWnd, nCmdShow);
    UpdateWindow(g_hMainWnd);
    
    // Message loop
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0) > 0)
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    // Cleanup fonts
    DeleteObject(g_titleFont);
    DeleteObject(g_counterFont);
    DeleteObject(g_buttonFont);
    
    return msg.wParam;
}

LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
    case WM_CREATE:
        CreateControls(hwnd);
        break;
        
    case WM_SIZE:
        ResizeControls(hwnd);
        break;
        
    case WM_COMMAND:
        {
            int wmId = LOWORD(wParam);
            
            if (wmId >= ID_INCREMENT_BASE && wmId < ID_INCREMENT_BASE + 9)
            {
                IncrementCounter(wmId - ID_INCREMENT_BASE);
            }
            else if (wmId >= ID_DECREMENT_BASE && wmId < ID_DECREMENT_BASE + 9)
            {
                DecrementCounter(wmId - ID_DECREMENT_BASE);
            }
            else if (wmId >= ID_RESET_BASE && wmId < ID_RESET_BASE + 9)
            {
                std::wstring message = L"Are you sure you want to reset counter " + std::to_wstring(wmId - ID_RESET_BASE + 1) + L"?";
                int result = MessageBox(hwnd, message.c_str(), TEXT("Reset Counter"), MB_YESNO | MB_ICONQUESTION);
                if (result == IDYES)
                {
                    ResetCounter(wmId - ID_RESET_BASE);
                }
            }
            else if (wmId == ID_RESET_ALL)
            {
                int result = MessageBox(hwnd, TEXT("Are you sure you want to reset all counters?"),
                    TEXT("Reset All Counters"), MB_YESNO | MB_ICONQUESTION);
                if (result == IDYES)
                {
                    ResetAllCounters();
                }
            }
        }
        break;

    case WM_DRAWITEM:
        {
            DRAWITEMSTRUCT* dis = (DRAWITEMSTRUCT*)lParam;
            if (dis->CtlID >= ID_COUNTER_BASE && dis->CtlID < ID_COUNTER_BASE + 9)
            {
                int counterIndex = dis->CtlID - ID_COUNTER_BASE;
                DrawCircularCounter(dis->hDC, dis->rcItem, counterIndex + 1, g_counterValues[counterIndex]);
                return TRUE;
            }
        }
        break;

    case WM_KEYDOWN:
        {
            if (wParam >= '1' && wParam <= '9')
            {
                IncrementCounter(wParam - '1');
            }
        }
        break;
        
    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);
            
            // Draw title
            SetBkMode(hdc, TRANSPARENT);
            SelectObject(hdc, g_titleFont);
            SetTextColor(hdc, RGB(50, 50, 100));
            
            RECT titleRect = {0, 10, WINDOW_WIDTH, 50};
            DrawText(hdc, TEXT("Counter Application"), -1, &titleRect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);

            RECT instrRect = {0, 40, WINDOW_WIDTH, 70};
            SelectObject(hdc, g_buttonFont);
            SetTextColor(hdc, RGB(100, 100, 100));
            DrawText(hdc, TEXT("Use number keys 1-9 to increment counters"), -1, &instrRect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
            
            EndPaint(hwnd, &ps);
        }
        break;
        
    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
    return 0;
}

void CreateControls(HWND hwnd)
{
    // Create counter displays (static controls for custom drawing)
    for (int i = 0; i < 9; i++)
    {
        g_counterLabels[i] = CreateWindow(TEXT("STATIC"), TEXT(""),
            WS_CHILD | WS_VISIBLE | SS_OWNERDRAW,
            0, 0, COUNTER_SIZE, COUNTER_SIZE,
            hwnd, (HMENU)(ID_COUNTER_BASE + i), g_hInst, NULL);
    }

    // Create increment buttons
    for (int i = 0; i < 9; i++)
    {
        g_incrementButtons[i] = CreateWindow(TEXT("BUTTON"), TEXT("+"),
            WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
            0, 0, BUTTON_WIDTH, BUTTON_HEIGHT,
            hwnd, (HMENU)(ID_INCREMENT_BASE + i), g_hInst, NULL);
        SendMessage(g_incrementButtons[i], WM_SETFONT, (WPARAM)g_buttonFont, TRUE);
    }

    // Create decrement buttons
    for (int i = 0; i < 9; i++)
    {
        g_decrementButtons[i] = CreateWindow(TEXT("BUTTON"), TEXT("-"),
            WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
            0, 0, BUTTON_WIDTH, BUTTON_HEIGHT,
            hwnd, (HMENU)(ID_DECREMENT_BASE + i), g_hInst, NULL);
        SendMessage(g_decrementButtons[i], WM_SETFONT, (WPARAM)g_buttonFont, TRUE);
    }

    // Create reset buttons
    for (int i = 0; i < 9; i++)
    {
        g_resetButtons[i] = CreateWindow(TEXT("BUTTON"), TEXT("Reset"),
            WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
            0, 0, BUTTON_WIDTH * 2 + 10, BUTTON_HEIGHT,
            hwnd, (HMENU)(ID_RESET_BASE + i), g_hInst, NULL);
        SendMessage(g_resetButtons[i], WM_SETFONT, (WPARAM)g_buttonFont, TRUE);
    }

    // Create reset all button
    g_resetAllButton = CreateWindow(TEXT("BUTTON"), TEXT("Reset All Counters"),
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        0, 0, 200, 40,
        hwnd, (HMENU)ID_RESET_ALL, g_hInst, NULL);
    SendMessage(g_resetAllButton, WM_SETFONT, (WPARAM)g_buttonFont, TRUE);
}

void ResizeControls(HWND hwnd)
{
    RECT clientRect;
    GetClientRect(hwnd, &clientRect);

    int clientWidth = clientRect.right - clientRect.left;
    int clientHeight = clientRect.bottom - clientRect.top;

    // Calculate grid dimensions
    int gridWidth = clientWidth - 2 * MARGIN;
    int gridHeight = clientHeight - 150; // Leave space for title and reset all button

    int cellWidth = gridWidth / GRID_SIZE;
    int cellHeight = gridHeight / GRID_SIZE;

    int counterSize = min(cellWidth - 20, cellHeight - 80) - 20; // Leave space for buttons
    counterSize = max(counterSize, 80); // Minimum size

    // Position counters and buttons
    for (int i = 0; i < 9; i++)
    {
        int row = i / GRID_SIZE;
        int col = i % GRID_SIZE;

        int x = MARGIN + col * cellWidth + (cellWidth - counterSize) / 2;
        int y = 80 + row * cellHeight + (cellHeight - counterSize - 80) / 2; // 80 for title space

        // Position counter display
        SetWindowPos(g_counterLabels[i], NULL, x, y, counterSize, counterSize,
                    SWP_NOZORDER);

        // Position increment and decrement buttons
        int buttonY = y + counterSize + 10;
        int button1X = x + (counterSize - BUTTON_WIDTH * 2 - 10) / 2;
        int button2X = button1X + BUTTON_WIDTH + 10;

        SetWindowPos(g_decrementButtons[i], NULL, button1X, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT,
                    SWP_NOZORDER);
        SetWindowPos(g_incrementButtons[i], NULL, button2X, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT,
                    SWP_NOZORDER);

        // Position reset button
        int resetButtonY = buttonY + BUTTON_HEIGHT + 5;
        int resetButtonX = x + (counterSize - (BUTTON_WIDTH * 2 + 10)) / 2;
        SetWindowPos(g_resetButtons[i], NULL, resetButtonX, resetButtonY, BUTTON_WIDTH * 2 + 10, BUTTON_HEIGHT,
                    SWP_NOZORDER);
    }

    // Position reset all button
    int resetAllX = (clientWidth - 200) / 2;
    int resetAllY = clientHeight - 60;
    SetWindowPos(g_resetAllButton, NULL, resetAllX, resetAllY, 200, 40, SWP_NOZORDER);
}

void UpdateCounterDisplay(int index)
{
    InvalidateRect(g_counterLabels[index], NULL, TRUE);
}

void IncrementCounter(int index)
{
    if (index >= 0 && index < 9)
    {
        g_counterValues[index]++;
        UpdateCounterDisplay(index);
    }
}

void DecrementCounter(int index)
{
    if (index >= 0 && index < 9 && g_counterValues[index] > 0)
    {
        g_counterValues[index]--;
        UpdateCounterDisplay(index);
    }
}

void ResetCounter(int index)
{
    if (index >= 0 && index < 9)
    {
        g_counterValues[index] = 0;
        UpdateCounterDisplay(index);
    }
}

void ResetAllCounters()
{
    for (int i = 0; i < 9; i++)
    {
        g_counterValues[i] = 0;
        UpdateCounterDisplay(i);
    }
}

LRESULT CALLBACK CounterProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);

            RECT rect;
            GetClientRect(hwnd, &rect);

            // Find which counter this is
            int counterIndex = -1;
            for (int i = 0; i < 9; i++)
            {
                if (g_counterLabels[i] == hwnd)
                {
                    counterIndex = i;
                    break;
                }
            }

            if (counterIndex >= 0)
            {
                DrawCircularCounter(hdc, rect, counterIndex + 1, g_counterValues[counterIndex]);
            }

            EndPaint(hwnd, &ps);
        }
        return 0;

    default:
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
}

void DrawCircularCounter(HDC hdc, RECT rect, int counterNum, int value)
{
    // Create brushes and pens
    HBRUSH backgroundBrush = CreateSolidBrush(RGB(240, 240, 255));
    HBRUSH oldBrush = (HBRUSH)SelectObject(hdc, backgroundBrush);
    HPEN borderPen = CreatePen(PS_SOLID, 3, RGB(100, 100, 150));
    HPEN oldPen = (HPEN)SelectObject(hdc, borderPen);

    // Draw circle
    int margin = 5;
    Ellipse(hdc, rect.left + margin, rect.top + margin,
            rect.right - margin, rect.bottom - margin);

    // Set text properties
    SetBkMode(hdc, TRANSPARENT);
    SetTextAlign(hdc, TA_CENTER | TA_BASELINE);

    // Draw counter number
    SelectObject(hdc, g_buttonFont);
    SetTextColor(hdc, RGB(80, 80, 120));
    int centerX = (rect.left + rect.right) / 2;
    int topY = rect.top + (rect.bottom - rect.top) / 4;

    std::wstring counterText = std::to_wstring(counterNum);
    TextOut(hdc, centerX, topY, counterText.c_str(), counterText.length());

    // Draw value
    SelectObject(hdc, g_counterFont);
    SetTextColor(hdc, RGB(50, 50, 100));
    int centerY = (rect.top + rect.bottom) / 2 + 10;

    std::wstring valueText = std::to_wstring(value);
    TextOut(hdc, centerX, centerY, valueText.c_str(), valueText.length());

    // Cleanup
    SelectObject(hdc, oldBrush);
    SelectObject(hdc, oldPen);
    DeleteObject(backgroundBrush);
    DeleteObject(borderPen);
}
