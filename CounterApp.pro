QT += core widgets

CONFIG += c++17

TARGET = CounterApp
TEMPLATE = app

# Source files
SOURCES += \
    main.cpp \
    mainwindow.cpp \
    counter.cpp

# Header files
HEADERS += \
    mainwindow.h \
    counter.h

# Set application properties
VERSION = 1.0.0
QMAKE_TARGET_COMPANY = "Counter App"
QMAKE_TARGET_PRODUCT = "Counter Application"
QMAKE_TARGET_DESCRIPTION = "A counter application with 9 circular counters"

# Windows specific settings
win32 {
    RC_ICONS = 
    CONFIG += windows
}

# macOS specific settings
macx {
    CONFIG += app_bundle
}

# Output directory
DESTDIR = $$PWD/bin
