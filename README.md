# Counter Application

A C++ GUI application with 9 circular counters arranged in a 3x3 grid, built with Qt6.

## Features

- **9 Circular Counters**: Arranged in a responsive 3x3 grid layout
- **Individual Controls**: Each counter has +/- buttons and a reset button
- **Keyboard Input**: Press numpad keys 1-9 to increment corresponding counters
- **Global Reset**: Reset all counters at once with confirmation dialog
- **Confirmation Dialogs**: All reset operations require user confirmation
- **Responsive Design**: Layout automatically adjusts to window size
- **Modern UI**: Clean, modern interface with hover effects

## Requirements

- Qt6 (Core and Widgets modules)
- CMake 3.16 or higher
- C++17 compatible compiler (GCC, Clang, or MSVC)

## Building the Application

### Option 1: CMake (Recommended)
1. Make sure Qt6 and CMake are installed and in your PATH
2. Run the build script: `build.bat` (Windows)
3. Or manually:
   ```
   mkdir build && cd build
   cmake ..
   cmake --build .
   ```

### Option 2: qmake (Qt's build system)
1. Make sure Qt6 with qmake is installed
2. Run the build script: `build_qmake.bat` (Windows)
3. Or manually:
   ```
   mkdir build_qmake && cd build_qmake
   qmake ../CounterApp.pro
   make
   ```

### Option 3: Qt Creator IDE
1. Open Qt Creator
2. File → Open File or Project
3. Select `CounterApp.pro` or `CMakeLists.txt`
4. Build and run

See `compile_instructions.md` for detailed compilation instructions and troubleshooting.

## Usage

1. **Increment Counters**: 
   - Click the "+" button below each counter
   - Press numpad keys 1-9 to increment the corresponding counter

2. **Decrement Counters**: 
   - Click the "-" button below each counter

3. **Reset Individual Counter**: 
   - Click the "Reset" button below each counter
   - Confirm the action in the dialog

4. **Reset All Counters**: 
   - Click the "Reset All Counters" button at the bottom
   - Confirm the action in the dialog

5. **Responsive Layout**: 
   - Resize the window to see the counters automatically adjust their size and position

## Project Structure

- `main.cpp` - Application entry point
- `mainwindow.h/cpp` - Main window with 3x3 grid layout
- `counter.h/cpp` - Individual counter widget with circular display
- `CMakeLists.txt` - CMake build configuration
- `build.bat` - Windows build script

## Counter Layout

The counters are numbered 1-9 and arranged as follows:

```
1  2  3
4  5  6
7  8  9
```

Each counter displays:
- Counter number (top of circle)
- Current value (center of circle)
- +/- buttons (below circle)
- Reset button (below +/- buttons)
