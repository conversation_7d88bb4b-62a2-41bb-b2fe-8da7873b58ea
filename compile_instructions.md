# Compilation Instructions

This project can be built using several different methods depending on your development environment.

## Method 1: CMake (Recommended)

### Prerequisites
- Qt6 installed with development tools
- CMake 3.16 or higher
- C++17 compatible compiler

### Steps
1. Open a terminal in the project directory
2. Run: `build.bat` (Windows) or follow manual steps below

### Manual CMake Build
```bash
mkdir build
cd build
cmake ..
cmake --build .
```

## Method 2: qmake (Qt's build system)

### Prerequisites
- Qt6 installed with qmake
- MinGW or MSVC compiler

### Steps
1. Open a terminal in the project directory
2. Run: `build_qmake.bat` (Windows) or follow manual steps below

### Manual qmake Build
```bash
mkdir build_qmake
cd build_qmake
qmake ../CounterApp.pro
make  # or mingw32-make on Windows
```

## Method 3: Qt Creator IDE

1. Open Qt Creator
2. File → Open File or Project
3. Select `CounterApp.pro` or `CMakeLists.txt`
4. Configure the project with your Qt kit
5. Build and run

## Method 4: Visual Studio (Windows)

1. Install Qt VS Tools extension
2. Open Visual Studio
3. Create new Qt project or import existing
4. Add all .cpp and .h files to the project
5. Configure Qt paths in project properties
6. Build and run

## Troubleshooting

### Qt Not Found
- Make sure Qt6 is installed
- Add Qt's bin directory to your PATH
- Set QTDIR environment variable if needed

### Compiler Issues
- Ensure you have a C++17 compatible compiler
- On Windows, MinGW or MSVC 2019+ recommended
- On Linux, GCC 7+ or Clang 5+
- On macOS, Xcode 10+ or Clang 5+

### Missing Dependencies
The application only requires Qt6 Core and Widgets modules. Make sure these are installed with your Qt installation.

## Running the Application

After successful compilation, run the executable:
- CMake build: `build/CounterApp.exe` (Windows) or `build/CounterApp` (Linux/macOS)
- qmake build: `build_qmake/release/CounterApp.exe` (Windows)

The application should open with a window containing 9 circular counters in a 3x3 grid.
