#ifndef COUNTER_H
#define COUNTER_H

#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPainter>
#include <QFont>
#include <QFontMetrics>
#include <QMessageBox>

class Counter : public QWidget
{
    Q_OBJECT

public:
    explicit Counter(int counterNumber, QWidget *parent = nullptr);
    
    int getValue() const { return m_value; }
    void setValue(int value);
    void increment();
    void decrement();
    void reset();
    
    int getCounterNumber() const { return m_counterNumber; }

protected:
    void paintEvent(QPaintEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    void onIncrementClicked();
    void onDecrementClicked();
    void onResetClicked();

private:
    void setupUI();
    void updateButtonSizes();
    QRect getCircleRect() const;
    
    int m_value;
    int m_counterNumber;
    
    QPushButton *m_incrementButton;
    QPushButton *m_decrementButton;
    QPushButton *m_resetButton;
    
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_buttonLayout;
    
    static const int MIN_CIRCLE_SIZE = 80;
    static const int BUTTON_HEIGHT = 30;
    static const int SPACING = 5;
};

#endif // COUNTER_H
