#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QGridLayout>
#include <QVBoxLayout>
#include <QPushButton>
#include <QWidget>
#include <QKeyEvent>
#include <QMessageBox>
#include <QLabel>
#include <array>
#include "counter.h"

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void keyPressEvent(QKeyEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    void onResetAllClicked();

private:
    void setupUI();
    void updateLayout();
    
    QWidget *m_centralWidget;
    QVBoxLayout *m_mainLayout;
    QGridLayout *m_gridLayout;
    QPushButton *m_resetAllButton;
    QLabel *m_titleLabel;
    QLabel *m_instructionLabel;
    
    std::array<Counter*, 9> m_counters;
    
    static const int GRID_ROWS = 3;
    static const int GRID_COLS = 3;
    static const int MIN_WINDOW_WIDTH = 600;
    static const int MIN_WINDOW_HEIGHT = 500;
};

#endif // MAINWINDOW_H
