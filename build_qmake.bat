@echo off
echo Building Counter Application with qmake...

REM Create build directory
if not exist build_qmake mkdir build_qmake
cd build_qmake

REM Generate Makefile with qmake
qmake ../CounterApp.pro
if %errorlevel% neq 0 (
    echo qmake failed! Make sure Qt is installed and in PATH.
    pause
    exit /b 1
)

REM Build the application
mingw32-make
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable is located at: build_qmake\release\CounterApp.exe
pause
