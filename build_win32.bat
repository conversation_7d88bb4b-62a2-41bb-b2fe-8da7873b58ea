@echo off
echo Building Counter Application (Windows API version)...

REM Try to find Visual Studio compiler
set "VSWHERE=%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe"
if exist "%VSWHERE%" (
    for /f "usebackq tokens=*" %%i in (`"%VSWHERE%" -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath`) do (
        set "VS_PATH=%%i"
    )
)

if defined VS_PATH (
    echo Found Visual Studio at: %VS_PATH%
    call "%VS_PATH%\VC\Auxiliary\Build\vcvars64.bat"
    cl /EHsc /O2 counter_app_win32.cpp /Fe:CounterApp.exe /link user32.lib gdi32.lib comctl32.lib
    if %errorlevel% equ 0 (
        echo Build successful! Executable: CounterApp.exe
    ) else (
        echo Build failed with Visual Studio compiler
    )
) else (
    echo Visual Studio not found. Trying with MinGW...
    
    REM Try MinGW
    where gcc >nul 2>&1
    if %errorlevel% equ 0 (
        echo Found GCC, compiling with MinGW...
        gcc -std=c++17 -O2 -mwindows counter_app_win32.cpp -o CounterApp.exe -luser32 -lgdi32 -lcomctl32
        if %errorlevel% equ 0 (
            echo Build successful! Executable: CounterApp.exe
        ) else (
            echo Build failed with MinGW
        )
    ) else (
        echo No suitable compiler found.
        echo Please install Visual Studio with C++ tools or MinGW-w64.
        echo.
        echo Alternative: Try online C++ compiler or use the Qt version if Qt is available.
    )
)

pause
